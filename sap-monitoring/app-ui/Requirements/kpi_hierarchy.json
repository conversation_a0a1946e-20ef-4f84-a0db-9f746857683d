[{"id": 3, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_IDLE", "kpiDescription": "CPU True Idle", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 4, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_LOAD15_AVG", "kpiDescription": "CPU Load - 15 Mins Average", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 5, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_LOAD1_AVG", "kpiDescription": "CPU Load - 1 Min Average", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 6, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_LOAD5_AVG", "kpiDescription": "CPU Load - 5 Mins Average", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 7, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_NBR", "kpiDescription": "Number of CPUs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 8, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_SYS", "kpiDescription": "CPU - System Utilization", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 9, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_S_IDLE", "kpiDescription": "Single CPU Idle Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 10, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_S_SERIAL", "kpiDescription": "Single CPU Serial No.", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 11, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_S_SYS", "kpiDescription": "Single CPU used by System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 12, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_S_USER", "kpiDescription": "Single CPU used by User", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 13, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_USR", "kpiDescription": "CPU - User Utilization", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 14, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "CPU_WAIT", "kpiDescription": "CPU I/O Waiting", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 15, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_OPS_SEC", "kpiDescription": "Average Disk operations per Second - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 16, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_QUEUE_LEN", "kpiDescription": "Average Disk Queue Length - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 17, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_RESP_TIME", "kpiDescription": "Average Disk response time - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 18, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_SERV_TIME", "kpiDescription": "Average Disk Service Time - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 19, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_NAME", "kpiDescription": "Name of the Disk", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 20, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_OPS_SEC", "kpiDescription": "Disk Operations per second", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 21, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_QUEUE_LEN", "kpiDescription": "Disk Avg. Queue length", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 22, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_RESP_TIME", "kpiDescription": "Disk Response time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 23, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_SERIALNR", "kpiDescription": "Serial No. - Single Disk", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 24, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_SERV_TIME", "kpiDescription": "Disk Avg. Service time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 25, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_TRANSFER", "kpiDescription": "Disk Transfer speed", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 26, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_UTIL", "kpiDescription": "Disk Utilization", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 27, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_S_WAIT_TIME", "kpiDescription": "Disk Avg. wait time - Single Disk", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 28, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_TRANSFER", "kpiDescription": "Average Disk Transfer Speed - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 29, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_UTIL", "kpiDescription": "Average Disk utilization - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 30, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "DISK_WAIT_TIME", "kpiDescription": "Average disk wait - System", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 31, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "FREE_MEM_PERC", "kpiDescription": "Free Physical memory percentage", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 32, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_COLLISSIONS", "kpiDescription": "LAN Collissions - System Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 33, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_IN_ERRORS", "kpiDescription": "LAN Errors Inbound - System Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 34, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_IN_PACKETS", "kpiDescription": "LAN Packets In - System Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 35, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_OUT_ERRORS", "kpiDescription": "LAN Errors outbound - System Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 36, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_OUT_PACKETS", "kpiDescription": "LAN Packets Out - System Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 37, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_COLLISSIONS", "kpiDescription": "LAN Collissions - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 38, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_IN_ERRORS", "kpiDescription": "LAN Errors Inbound - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 39, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_IN_PACKETS", "kpiDescription": "LAN Packets In - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 40, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_LANNAME", "kpiDescription": "LAN Name - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 41, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_OUT_ERRORS", "kpiDescription": "LAN Errors Outbound - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 42, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_OUT_PACKETS", "kpiDescription": "LAN Packets Out - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 43, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "LAN_S_SERIALNR", "kpiDescription": "LAN Serial No. - Node Level", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 44, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "MEM_FREE", "kpiDescription": "Free Physical memory", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 45, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "MEM_PHYS", "kpiDescription": "Total Physical memory", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 46, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "PAGE_IN", "kpiDescription": "Pages IN in kB/s", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 47, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "PAGE_OUT", "kpiDescription": "Pages OUT in kB/s", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 48, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "SWAP_CONF", "kpiDescription": "Configured Swap memory size", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 49, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "SWAP_FREE", "kpiDescription": "Free Swap memory size", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 50, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "SWAP_FREE_PERC", "kpiDescription": "Free Swap Memory percentage", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 51, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "SWAP_MAX", "kpiDescription": "Maximum Swap size", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 52, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "SWAP_SIZE", "kpiDescription": "Actual Swap size", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 53, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_CPU_TIME_AVG", "kpiDescription": "Top 40 Processes - CPU Time Average", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 54, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_MEM_UTIL_AVG", "kpiDescription": "Top 40 Processes - Memory Util. Average", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 55, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_COMMAND", "kpiDescription": "Top 40 CPU Processes - Process Name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 56, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_CPU_TIME", "kpiDescription": "Top 40 CPU Processes - CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 57, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_CPU_UTIL", "kpiDescription": "Top 40 CPU Processes - CPU Utilization %", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 58, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_PRIORITY", "kpiDescription": "Top 40 CPU Processes - Priority", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 59, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_PRIV_PAGES", "kpiDescription": "Top 40 CPU Processes - Process Pages", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 60, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_PROC_ID", "kpiDescription": "Top 40 CPU Processes - Process ID", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 61, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_RES_SIZE", "kpiDescription": "Top 40 CPU Processes - Memory Utilization", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 62, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_SERIALNR", "kpiDescription": "Top 40 CPU Processes - Serial No.", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 63, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "OS", "kpiName": "TOP_S_USERNAME", "kpiDescription": "Top 40 CPU Processes - Username", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 64, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_DELAYED", "kpiDescription": "Number of  Background Jobs delayed", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 65, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_CANCELLED", "kpiDescription": "Number of canvelled Background Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 66, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_MAX_DELAY", "kpiDescription": "Maximum delay to start a Job", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 67, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_DELAYED_TIME", "kpiDescription": "Total delay for the run period ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 68, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_COMPLETED", "kpiDescription": "Number of completed Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 69, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_ACTIVE", "kpiDescription": "Number of Active Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 70, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_TOTAL", "kpiDescription": "Number of Total Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 71, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_SCHEDULED", "kpiDescription": "Number of Scheduled Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 72, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_RELEASED", "kpiDescription": "Number of Released Jobs", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 73, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_MAX_RUNTIME", "kpiDescription": "Maximum Runtime taken by a Job", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 74, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_NAME        ", "kpiDescription": "Job Name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 75, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_COUNT       ", "kpiDescription": "Job Counter", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 76, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_SCHD_STRTDT ", "kpiDescription": "Job Scheduled Start Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 77, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_SCHD_STRTTM ", "kpiDescription": "Job Scheduled Start Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 78, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_LASTCHDT    ", "kpiDescription": "Job Last changed Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 79, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_LASTCHTM    ", "kpiDescription": "Job Last changed Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 80, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_REL_DATE    ", "kpiDescription": "Job Release Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 81, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_REL_TIME    ", "kpiDescription": "Job Release Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 82, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_ACT_STRTDT  ", "kpiDescription": "Job Actual Start Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 83, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_ACT_STRTTM  ", "kpiDescription": "Job Actual Start Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 84, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_ACT_ENDDT   ", "kpiDescription": "Job Actual End Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 85, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_ACT_ENDTM   ", "kpiDescription": "Job Actual End Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 86, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_STATUS      ", "kpiDescription": "Job Status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 87, "sid": "SW1", "monitoringArea": "JOBS", "kpiGroup": "JOBS", "kpiName": "JOB_S_SERVER", "kpiDescription": "Job execution Server", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 88, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_CHANGED_CNT", "kpiDescription": "IDOCs changed", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 89, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_INB_CNT      ", "kpiDescription": "IDOC - Inbound count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 90, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_INB_ERR_CNT ", "kpiDescription": "IDOC - Inbound Error count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 91, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_INB_PROC_AVG", "kpiDescription": "IDOC - Inbound Average Processing time ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 92, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_OB_CNT    ", "kpiDescription": "IDOC - Outbound count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 93, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_OB_ERR_CNT   ", "kpiDescription": "IDOC - Outbound Error count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 94, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_OB_PROC_AVG  ", "kpiDescription": "IDOC - Outbound Average Processing time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 95, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_OB_TRANS_AVG ", "kpiDescription": "IDOC  - Average Transmission Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 96, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_MESTYP", "kpiDescription": "IDOC Message Type", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 97, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_IDOCTP", "kpiDescription": "IDOC Type", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 98, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_DOCNUM", "kpiDescription": "IDOC Document No.", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 99, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_DIRECT", "kpiDescription": "IDOC Direction", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 100, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_CREDAT", "kpiDescription": "IDOC - Creation Date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 101, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_CRETIM", "kpiDescription": "IDOC - Creation Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 102, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_STATUS", "kpiDescription": "IDOC - Status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 103, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_UPDDAT", "kpiDescription": "IDOC - Update date", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 104, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_UPDTIM", "kpiDescription": "IDOC - Update Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 105, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_RCVPRN", "kpiDescription": "IDOC - Receiver Port", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 106, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_SNDPRN", "kpiDescription": "IDOC - Sender Port", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 107, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "IDOCS", "kpiName": "IDOC_S_STATXT", "kpiDescription": "IDOC - Status Text", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 108, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_VERY_IMP", "kpiDescription": "Application Log - Problem Class Very Important", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 109, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_IMP", "kpiDescription": "Application Log - Problem Class Important", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 110, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_MEDIUM", "kpiDescription": "Application Log - Problem Class Medium", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 111, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_USER_MAX", "kpiDescription": "Application Log - Maximum logs with Single User", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 112, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_BATCH_PROC", "kpiDescription": "Application Log - Batch Processing", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 113, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_DIALOG_PROC", "kpiDescription": "Application Log - Dialog Processing", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 114, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_BI_PROC", "kpiDescription": "Application Log - Batch Input Processing", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 115, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_AUTO_ABAP_PROC", "kpiDescription": "Application Log - Auto ABAP Processing", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 116, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_PROG_MAX", "kpiDescription": "Application Log - Maximum logs with Single Program", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 117, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_TCODE_MAX", "kpiDescription": "Application Log - Maximum logs with Single Tcode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 118, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_LOG_NUMBER", "kpiDescription": "Application Log Number", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 119, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_LOG_TIMESTMP", "kpiDescription": "Application Log - Time Stamp", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 120, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_LOG_MSGID", "kpiDescription": "Application Log - Message Class", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 121, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_LOG_MSGTY", "kpiDescription": "Application Log - Message Type", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 122, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S-LOG_MSGNO", "kpiDescription": "Application Log - Message Number", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 123, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S-LOG_MSGTXT", "kpiDescription": "Application Log - Message Text", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 124, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_LOG_USER", "kpiDescription": "Application Log - User name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 125, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_PROBCLASS", "kpiDescription": "Application Log- Problem Class", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 126, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_ALMODE", "kpiDescription": "Application Log - Processing Mode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 127, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_ALTCODE", "kpiDescription": "Application Log - Transaction Code", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 128, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "ALOGS", "kpiName": "ALOG_S_ALPROG", "kpiDescription": "Application Log - Program", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 129, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_DIA", "kpiDescription": "System Log - Dialog Workprocess ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 130, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_BTC", "kpiDescription": "System Log - Background Workprocess ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 131, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_ENQ", "kpiDescription": "System Log - Enqueue Workprocess ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 132, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_MS", "kpiDescription": "System Log - Message Server", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 133, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_RD", "kpiDescription": "System Log - Gateway", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 134, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_SPO", "kpiDescription": "System Log - Spool", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 135, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_UPD", "kpiDescription": "System Log - Update Work Process", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 136, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_VERY_IMP", "kpiDescription": "System Log - Very Important", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 137, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_IMP", "kpiDescription": "System Log - Important", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 138, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_MEDIUM", "kpiDescription": "System Log - Medium", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 139, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_PROG_MAX", "kpiDescription": "System Log - Maximum logs with Single Program", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 140, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_TCODE_MAX", "kpiDescription": "System Log - Maximum logs with Single Tcode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 141, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_USER_MAX", "kpiDescription": "System Log - Maximum logs with Single User", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 142, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_INSTANCE", "kpiDescription": "System Log - Instance name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 143, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_WP_TYPE", "kpiDescription": "System Log - Work Process Type", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 144, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_PROG", "kpiDescription": "System Log - Program", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 145, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_TCODE", "kpiDescription": "System Log - Transaction Code", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 146, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_USER", "kpiDescription": "System Log - User name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 147, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_SEVERITY", "kpiDescription": "System Log - Severity", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 148, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_MESSAGEID", "kpiDescription": "System Log - Message ID", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 149, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "SLOGS", "kpiName": "SLOG_S_TEXT", "kpiDescription": "System Log - Message Text", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 150, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_CURR_LOCKS", "kpiDescription": "ENQ - Current Number of Locks", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 151, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_PEAK_LOCKS", "kpiDescription": "ENQ - Peak Number of Locks", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 152, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_CONFIG_LOCKS", "kpiDescription": "ENQ - Configured Number of Locks", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 153, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_TOTAL_CNT_LOCKS", "kpiDescription": "ENQ - Total Number of Locks", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 154, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_COLLISIONS_LOCK", "kpiDescription": "ENQ - Lock Collisions", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 155, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OVERFLOWS_SESSION", "kpiDescription": "ENQ - Session Overflows", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 156, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OVERFLOWS_LOCK_TAB", "kpiDescription": "ENQ - Lock Table Overflows", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 157, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_TOTAL_LOCK_OPS_CNT", "kpiDescription": "ENQ - Total No. of Lock Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 158, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_LOCK_PROCESS_TIME", "kpiDescription": "ENQ - Processing Time for Lock Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 159, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_LOCK_WAIT_TIME", "kpiDescription": "ENQ - Waiting Time for Lock Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 160, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_ENQUEUE", "kpiDescription": "ENQ - Enqueue Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 161, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_DEQUEUE", "kpiDescription": "ENQ - Dequeue Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 162, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_ALL_DEQUEUE", "kpiDescription": "ENQ - Dequeue All Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 163, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_REMOVE", "kpiDescription": "ENQ - Remove Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 164, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_BACKUP", "kpiDescription": "ENQ - Backup Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 165, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_READ", "kpiDescription": "ENQ - Read Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 166, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_ROPO", "kpiDescription": "ENQ - RemoveOwnerPattern Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 167, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OP_RAPO", "kpiDescription": "ENQ - RemoveArgumentPattern Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 168, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_OPS_CONF_REPL", "kpiDescription": "ENQ - Configure Replica Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 169, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_UPDATE_REPL_OPS", "kpiDescription": "ENQ - Update Replica Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 170, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_TAKEOVER_REPL_OPS", "kpiDescription": "ENQ - Takeover Replica Operations", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 171, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_ROLL_OUT_CONN", "kpiDescription": "ENQ - Roll-Outs of Connection", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 172, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_ROLL_IN_CONN", "kpiDescription": "ENQ - Roll-Ins of Connection", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 173, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_ERRORS_SERVER", "kpiDescription": "ENQ - Server Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 174, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_CONN_ERRORS", "kpiDescription": "ENQ - Connection Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 175, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_TIMEOUTS", "kpiDescription": "ENQ - Timeouts", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 176, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_NETWORK_REQ", "kpiDescription": "ENQ - Network Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 177, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_RECD_DATA", "kpiDescription": "ENQ - Received Data", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 178, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_SENT_DATA", "kpiDescription": "ENQ - Sent Data", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 179, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_SER_CPU_TIME", "kpiDescription": "ENQ - CPU Time of Enqueue Server", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 180, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_BACKUP_REC_WRITTEN", "kpiDescription": "ENQ - Backup Records Written", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 181, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_ADMIN_REQ", "kpiDescription": "ENQ - Administration Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 182, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_MAX_NUM_SEARCH", "kpiDescription": "ENQ - Maximum Number in Search Result", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 183, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_AVG_NUM_SEARCH", "kpiDescription": "ENQ - Average Number in Search Result", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 184, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_MAX_NUM_UPDATE", "kpiDescription": "ENQ - Maximum Number in Update Replica", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 185, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_AVG_NUM_UPDATE", "kpiDescription": "ENQ - Average Number in Update Replica", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 186, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_MAX_NUM_TAKEOVER", "kpiDescription": "ENQ - Maximum Number in Takeover Replica", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 187, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ES2_AVG_NUM_TAKEOVER", "kpiDescription": "ENQ - Average Number in Takeover Replica", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 188, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_REQ_CNT", "kpiDescription": "ENQ - Enqueue Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 189, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_REQ_REJ_CNT", "kpiDescription": "ENQ - Rejects", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 190, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ERR_CNT", "kpiDescription": "ENQ - Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 191, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " DEQ_REQ_CNT", "kpiDescription": "ENQ - Dequeue Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 192, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " DEQ_ERR_CNT", "kpiDescription": "ENQ - Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 193, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " DEQ_ALL_REQ_CNT", "kpiDescription": "ENQ - DequeueAll Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 194, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_CLEAN_REQ", "kpiDescription": "ENQ - CleanUp Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 195, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_BACKUP_REQ", "kpiDescription": "ENQ - Backup Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 196, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_REPORT_REQ", "kpiDescription": "ENQ - Reporting Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 197, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_COMPR_REQ", "kpiDescription": "ENQ - Compress Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 198, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_VERIFY_REQ", "kpiDescription": "ENQ - Verify Requests", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 199, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_FILE_WRITE", "kpiDescription": "ENQ - Writes to File", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 200, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_FILE_WRT_BACKUP", "kpiDescription": "ENQ - Backup", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 201, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_OWN_MAX_CNT", "kpiDescription": "ENQ - Owner Names", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 202, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_OWN_MAX_LVL", "kpiDescription": "ENQ - Peak Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 203, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_OWN_NOW_LVL", "kpiDescription": "ENQ - Actual Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 204, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ARG_MAX_CNT", "kpiDescription": "ENQ - Granule Arguments", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 205, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ARG_MAX_LVL", "kpiDescription": "ENQ - Peak Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 206, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ARG_NOW_LVL", "kpiDescription": "ENQ - Actual Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 207, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ENTR_MAX_CNT", "kpiDescription": "ENQ - Granule Entries", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 208, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ENTR_MAX_LVL", "kpiDescription": "ENQ - Peak Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 209, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_ENTR_NOW_LVL", "kpiDescription": "ENQ - Actual Util", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 210, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_UPD_MAX_CNT", "kpiDescription": "ENQ - Update Queue Peak", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 211, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_UPD_NOW_LVL", "kpiDescription": "ENQ - Actual", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 212, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_TOTAL_TIME", "kpiDescription": "ENQ - Total Lock Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 213, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_TOTAL_WAIT", "kpiDescription": "ENQ - Total Lock Wait Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 214, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": " ENQ_TOT_SER_TIME", "kpiDescription": "ENQ - Total Server Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 215, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_LONGEST_DURATION", "kpiDescription": "Duration of the longest lock entry", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 216, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_ALL", "kpiDescription": "Number of all existing lock entries", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 217, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_OBJECT", "kpiDescription": "Number of all existing lock Objects", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 218, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_USER_MAX", "kpiDescription": "Maximum number of locks with a single user", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 219, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_TCODE_MAX", "kpiDescription": "Maximum number of locks with a single Transaction code", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 220, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_MODE_E", "kpiDescription": "Number of exclusive locks (lock mode E)", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 221, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_MODE_O", "kpiDescription": "Number of optimistic locks (lock mode O)", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 222, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_MODE_S", "kpiDescription": "Number of shared locks (lock mode S)", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 223, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_NUM_MODE_X", "kpiDescription": "Number of exclusive non-cumulative locks (lock mode X)", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 224, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GNAME", "kpiDescription": "Elementary Lock of Lock Entry (Table Name)", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 225, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GMODE", "kpiDescription": "Lock Mode (Shared/Exclusive) of a Lock Entry", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 226, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GOBJ", "kpiDescription": "Name of Lock Object in the Lock Entry", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 227, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GUNAME", "kpiDescription": "User name in lock entry", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 228, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GTCODE", "kpiDescription": "Transaction Code in the Lock Entry", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 229, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "ENQ", "kpiName": "LOCK_S_GTHOST", "kpiDescription": "Host Name in the Lock Owner ID", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 230, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_BTC_NUM", "kpiDescription": "Number of Background Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 231, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_BTC_MAX_RUN", "kpiDescription": "Maximum Runtime of a Background Process", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 232, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_BTC_USED", "kpiDescription": "Number of Used Background Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 233, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_BTC_USED_PERC", "kpiDescription": "Percentage of Used Background Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 234, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_COMMIT", "kpiDescription": "Work Processes in COMMIT State", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 235, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_DIA_NUM", "kpiDescription": "Number of Dialog Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 236, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_DIA_MAX_RUN", "kpiDescription": "Maximum Runtime of a Dialog Work Process", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 237, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_DIA_USED", "kpiDescription": "Number of Used Dialog Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 238, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_DIA_USED_PERC", "kpiDescription": "Percentage of Used Dialog Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 239, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_MEM_MAX", "kpiDescription": "Work Processes - Maximum Memory Used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 240, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_TCODE_MAX", "kpiDescription": "Maximum Work Processes executing same Tcode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 241, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_PROG_MAX", "kpiDescription": "Maximum Work Processes executing same Program", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 242, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_BTCJOB_MAX", "kpiDescription": "Maximum Work Processes executing same Background Job", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 243, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_USER_MAX", "kpiDescription": "Maximum Work Processes used by same User", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 244, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_ENQ_NUM", "kpiDescription": "Number of Enqueue Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 245, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_ENQ_USED", "kpiDescription": "Number of Used Enqueue Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 246, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_UP2_NUM", "kpiDescription": "Number of Update-2 Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 247, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_UP2_USED", "kpiDescription": "Number of Used Update-2 Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 248, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_UPD_NUM", "kpiDescription": "Number of Update Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 249, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_UPD_USED", "kpiDescription": "Number of Used Update Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 250, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_SPO_NUM", "kpiDescription": "Number of Spool Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 251, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_SPO_USED", "kpiDescription": "Number of Used Spool Work Processes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 252, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_RUNNING", "kpiDescription": "Workprocesses in Running status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 253, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_WAITING", "kpiDescription": "Workprocesses in Waiting status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 254, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_STOPPED", "kpiDescription": "Workprocesses in Stopped status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 255, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_ENDED", "kpiDescription": "Workprocesses in Ended status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 256, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_SHUTDOWN", "kpiDescription": "Workprocesses in Shutdown status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 257, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_STANDBY", "kpiDescription": "Workprocesses in Standby status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 258, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_ONHOLD", "kpiDescription": "Workprocesses in OnHold status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 259, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_SERVER", "kpiDescription": "Workprocesses :  Server name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 260, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_BTCJOBNAME", "kpiDescription": "Workprocesses : Background Job name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 261, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_BNAME", "kpiDescription": "Workprocesses : User name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 262, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_REPORT", "kpiDescription": "Workprocesses : Program Name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 263, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_TCODE", "kpiDescription": "Workprocesses : Transaction Code", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 264, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_MEMSUM", "kpiDescription": "Workprocesses : Memory consumed", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 265, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_TYP", "kpiDescription": "Workprocesses : Type", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 266, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_STATUS", "kpiDescription": "Workprocesses : Status", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 267, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "WP", "kpiName": "WP_S_ELTIME", "kpiDescription": "Workprocesses : Elapsed time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 268, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_TOTAL_CPU_USER_TIME", "kpiDescription": "HDB - CPU time spent in user mode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 269, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_TOTAL_CPU_SYSTEM_TIME", "kpiDescription": "HDB - CPU time spent in kernel mode", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 270, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_TOTAL_CPU_WIO_TIME", "kpiDescription": "HDB - CPU time spent in wait IO ", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 271, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_TOTAL_CPU_IDLE_TIME", "kpiDescription": "HDB - CPU idle time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 272, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_FREE_PHYSICAL_MEMORY", "kpiDescription": "HDB - Free Physical memory", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 273, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_FREE_SWAP_SPACE", "kpiDescription": "HDB - Free Swap Space", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 274, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_USED_PHYSICAL_MEMORY", "kpiDescription": "HDB - Used Physical Memory", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 275, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_USED_SWAP_SPACE", "kpiDescription": "HDB - Used Swap Space", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 276, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_INST_TOT_MEM_USED_SIZE", "kpiDescription": "HDB - Memory used from Pool", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 277, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_INST_TOT_MEM_PEAK_USED", "kpiDescription": "HDB - Peak Memory used from Pool", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 278, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_OPEN_FILE_COUNT", "kpiDescription": "HDB - Open File Count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 279, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_DISK_DATA_USED", "kpiDescription": "HDB - Disk Data used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 280, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_DISK_DBCKUP_USED", "kpiDescription": "HDB - Disk Backup used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 281, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_DISK_LOG_USED", "kpiDescription": "HDB - Disk Log used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 282, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_DISK_LBCKUP_USED", "kpiDescription": "HDB - Disk Log Backup used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 283, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_DISK_TRACE_USED", "kpiDescription": "HDB - Disk Trace used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 284, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_CACHE_P", "kpiDescription": "HDB - Cache Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 285, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_CS_P", "kpiDescription": "HDB - Column Store Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 286, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_MS_P", "kpiDescription": "HDB - Monitoring & STAT Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 287, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_RS_P", "kpiDescription": "HDB - Row Store Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 288, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_SEI_P", "kpiDescription": "HDB - Statement Execution Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 289, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_SYSTEM_P", "kpiDescription": "HDB - System Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 290, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_CODE_P", "kpiDescription": "HDB - Code Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 291, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_MEM_STACK_P", "kpiDescription": "HDB - Stack Component Memory used", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 292, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_ALERT_ERROR", "kpiDescription": "HDB - <PERSON>ert - <PERSON><PERSON>r", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 293, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_ALERT_HIGH", "kpiDescription": "HDB -  Alert - High", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 294, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_ALERT_INFORMATION", "kpiDescription": "HDB - Alert - Information", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 295, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_ALERT_LOW", "kpiDescription": "HDB - Alert - Low", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 296, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_ALERT_MEDIUM", "kpiDescription": "HDB - Alert - Medium", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 297, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_S_ALERT_DETAILS", "kpiDescription": "HDB - <PERSON><PERSON>", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 298, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_S_ALERT_NAME", "kpiDescription": "HDB - <PERSON>ert <PERSON>", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 299, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_S_ALERT_RATING", "kpiDescription": "HDB - <PERSON><PERSON>", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 300, "sid": "SW1", "monitoringArea": "DATABASE", "kpiGroup": "HDB", "kpiName": "HDB_S_ALERT_USERACTION", "kpiDescription": "HDB - Alert User Action", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 301, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_INTERNAL ", "kpiDescription": "Runtime Error - Internal Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 302, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_ABAP_RT  ", "kpiDescription": "Runtime Error - ABAP Runtime Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 303, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_DYNP_RT  ", "kpiDescription": "Runtime Error - Dynpro Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 304, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_ABAP_PR  ", "kpiDescription": "Runtime Error - ABAP Programming Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 305, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_RES_BN   ", "kpiDescription": "Runtime Error - <PERSON> errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 306, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_ITS      ", "kpiDescription": "Runtime Error - ITS errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 307, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_SQL      ", "kpiDescription": "Runtime Error - Open SQL Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 308, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_DI       ", "kpiDescription": "Runtime Error - Databse Interface errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 309, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_COMM     ", "kpiDescription": "Runtime Error - Communication errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 310, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_OTHER    ", "kpiDescription": "Runtime Error - Other errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 311, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_USER_MAX ", "kpiDescription": "Runtime Error - Maximum errors single user", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 312, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_S_UNAME", "kpiDescription": "Runtime Error - User name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 313, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_S_HOST", "kpiDescription": "Runtime Error - Host name", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 314, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_S_ERRID", "kpiDescription": "Runtime Error - Error Identifier", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 315, "sid": "SW1", "monitoringArea": "LOGS", "kpiGroup": "RUNERR", "kpiName": "RUNERR_S_CATEGORY", "kpiDescription": "Runtime Error - Error Category", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 316, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_ARFC_FREE_P", "kpiDescription": "RFC - Available resource %", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 317, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_IN_ENTRIES", "kpiDescription": "RFC - Entries in Inbound Queue", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 318, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_IN_COUNT", "kpiDescription": "RFC - Inbound Queue Count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 319, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_OUT_ENTRIES", "kpiDescription": "RFC - Entries in Outbound Queue", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 320, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_OUT_COUNT", "kpiDescription": "RFC - Outbound Queue Count", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 321, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_IN_ERRORS", "kpiDescription": "RFC - QRFC Inbound Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 322, "sid": "SW1", "monitoringArea": "INTERFACES", "kpiGroup": "RFC", "kpiName": "RFC_QRFC_OUT_ERRORS", "kpiDescription": "RFC - QRFC Outbound Errors", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 323, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_DB_NUM_MOD", "kpiDescription": "STAT - Dialog DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 324, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_DB_AVG_MOD_TIME", "kpiDescription": "STAT - Dialog DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 325, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_DB_NUM_READS", "kpiDescription": "STAT - Dialog DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 326, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_DB_AVG_READ_TIME", "kpiDescription": "STAT - Dialog DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 327, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_AVG_RESP_TIME", "kpiDescription": "STAT - Dialog Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 328, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_AVG_PROC_TIME", "kpiDescription": "STAT - Dialog Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 329, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_DIA_AVG_CPU_TIME", "kpiDescription": "STAT - Dialog Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 330, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_DB_NUM_MOD", "kpiDescription": "STAT - Update DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 331, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_DB_AVG_MOD_TIME", "kpiDescription": "STAT - Update DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 332, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_DB_NUM_READS", "kpiDescription": "STAT - Update DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 333, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_DB_AVG_READ_TIME", "kpiDescription": "STAT - Update DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 334, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_AVG_RESP_TIME", "kpiDescription": "STAT - Update Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 335, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_AVG_PROC_TIME", "kpiDescription": "STAT - Update Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 336, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_UPD_AVG_CPU_TIME", "kpiDescription": "STAT - Update Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 337, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_DB_NUM_MOD", "kpiDescription": "STAT - Spool DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 338, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_DB_AVG_MOD_TIME", "kpiDescription": "STAT - Spool DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 339, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_DB_NUM_READS", "kpiDescription": "STAT - Spool DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 340, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_DB_AVG_READ_TIME", "kpiDescription": "STAT - Spool DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 341, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_AVG_RESP_TIME", "kpiDescription": "STAT - Spool Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 342, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_AVG_PROC_TIME", "kpiDescription": "STAT - Spool Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 343, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_SPO_AVG_CPU_TIME", "kpiDescription": "STAT - Spool Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 344, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_DB_NUM_MOD", "kpiDescription": "STAT - Batch DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 345, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_DB_AVG_MOD_TIME", "kpiDescription": "STAT - Batch DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 346, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_DB_NUM_READS", "kpiDescription": "STAT - Batch DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 347, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_DB_AVG_READ_TIME", "kpiDescription": "STAT - Batch DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 348, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_AVG_RESP_TIME", "kpiDescription": "STAT - Batch Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 349, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_AVG_PROC_TIME", "kpiDescription": "STAT - Batch Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 350, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_BATCH_AVG_CPU_TIME", "kpiDescription": "STAT - Batch Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 351, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_DB_NUM_MOD", "kpiDescription": "STAT - RFC DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 352, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_DB_AVG_MOD_TIME", "kpiDescription": "STAT - RFC DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 353, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_DB_NUM_READS", "kpiDescription": "STAT - RFC DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 354, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_DB_AVG_READ_TIME", "kpiDescription": "STAT - RFC DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 355, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_AVG_RESP_TIME", "kpiDescription": "STAT - RFC Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 356, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_AVG_PROC_TIME", "kpiDescription": "STAT - RFC Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 357, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_RFC_AVG_CPU_TIME", "kpiDescription": "STAT - RFC Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 358, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_DB_NUM_MOD", "kpiDescription": "STAT - HTTP DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 359, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_DB_AVG_MOD_TIME", "kpiDescription": "STAT - HTTP DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 360, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_DB_NUM_READS", "kpiDescription": "STAT - HTTP DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 361, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_DB_AVG_READ_TIME", "kpiDescription": "STAT - HTTP DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 362, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_AVG_RESP_TIME", "kpiDescription": "STAT - HTTP Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 363, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_AVG_PROC_TIME", "kpiDescription": "STAT - HTTP Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 364, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTP_AVG_CPU_TIME", "kpiDescription": "STAT - HTTP Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 365, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_DB_NUM_MOD", "kpiDescription": "STAT - HTTPS DB No. of Changes", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 366, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_DB_AVG_MOD_TIME", "kpiDescription": "STAT - HTTPS DB Avg. Modification Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 367, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_DB_NUM_READS", "kpiDescription": "STAT - HTTPS DB No. of Reads", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 368, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_DB_AVG_READ_TIME", "kpiDescription": "STAT - HTTPS DB Average Read Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 369, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_AVG_RESP_TIME", "kpiDescription": "STAT - HTTPS Average Response Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 370, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_AVG_PROC_TIME", "kpiDescription": "STAT - HTTPS Average Processing Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}, {"id": 371, "sid": "SW1", "monitoringArea": "SYSTEM", "kpiGroup": "STAT", "kpiName": "STAT_HTTPS_AVG_CPU_TIME", "kpiDescription": "STAT - HTTPS Average CPU Time", "createdAt": "2025-03-09 11:50:47.43", "updatedAt": null}]