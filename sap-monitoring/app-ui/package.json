{"name": "sap-monitoring", "version": "1.0.0", "private": true, "workspaces": ["sap-monitoring/app-ui"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/react": "^1.2.11", "@dnd-kit/sortable": "^10.0.0", "@prisma/client": "^5.10.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/typography": "^0.5.16", "ai": "^4.3.5", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "file-saver": "^2.0.5", "framer-motion": "^12.4.10", "jspdf": "^3.0.0", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "next": "14.2.1", "next-themes": "^0.4.4", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "reactflow": "^11.11.4", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-grid-layout": "^1.3.5", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-config-next": "14.2.1", "postcss": "^8.4.31", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}