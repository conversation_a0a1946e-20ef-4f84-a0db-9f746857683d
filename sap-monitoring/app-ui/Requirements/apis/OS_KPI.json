[{"kpi_name": "cpu_s_serial", "kpi_desc": "Single CPU Serial No.", "kpi_group": "OS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_sys", "kpi_desc": "Single CPU used by System", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_ops_sec", "kpi_desc": "Disk Operations per second", "kpi_group": "OS", "parent": "", "unit": "/S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_resp_time", "kpi_desc": "Disk Response time", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serv_time", "kpi_desc": "Disk Avg. Service time", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_transfer", "kpi_desc": "Disk Transfer speed", "kpi_group": "OS", "parent": "", "unit": "KB/S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_util", "kpi_desc": "Disk Utilization", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_wait_time", "kpi_desc": "Disk Avg. wait time - Single Disk", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_collissions", "kpi_desc": "LAN Collissions - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_errors", "kpi_desc": "LAN Errors Inbound - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_packets", "kpi_desc": "LAN Packets In - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_serialnr", "kpi_desc": "LAN Serial No. - Node Level", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_time", "kpi_desc": "Top 40 CPU Processes - CPU Time", "kpi_group": "OS", "parent": "", "unit": "S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_proc_id", "kpi_desc": "Top 40 CPU Processes - Process ID", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_res_size", "kpi_desc": "Top 40 CPU Processes - Memory Utilization", "kpi_group": "OS", "parent": "", "unit": "KB", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_idle", "kpi_desc": "Single CPU Idle Time", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_sys", "kpi_desc": "Single CPU used by System", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_queue_len", "kpi_desc": "Disk Avg. Queue length", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serv_time", "kpi_desc": "Disk Avg. Service time", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_transfer", "kpi_desc": "Disk Transfer speed", "kpi_group": "OS", "parent": "", "unit": "KB/S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_util", "kpi_desc": "Disk Utilization", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_wait_time", "kpi_desc": "Disk Avg. wait time - Single Disk", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_collissions", "kpi_desc": "LAN Collissions - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_errors", "kpi_desc": "LAN Errors Inbound - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_packets", "kpi_desc": "LAN Packets In - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_lanname", "kpi_desc": "LAN Name - Node Level", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_serialnr", "kpi_desc": "LAN Serial No. - Node Level", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_util", "kpi_desc": "Top 40 CPU Processes - CPU Utilization %", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_proc_id", "kpi_desc": "Top 40 CPU Processes - Process ID", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_res_size", "kpi_desc": "Top 40 CPU Processes - Memory Utilization", "kpi_group": "OS", "parent": "", "unit": "KB", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_serialnr", "kpi_desc": "Top 40 CPU Processes - Serial No.", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_load15_avg", "kpi_desc": "CPU Load - 15 Mins Average", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_load1_avg", "kpi_desc": "CPU Load - 1 Min Average", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_sys", "kpi_desc": "Single CPU used by System", "kpi_group": "OS", "parent": false, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_usr", "kpi_desc": "CPU - User Utilization", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_wait", "kpi_desc": "CPU I/O Waiting", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_resp_time", "kpi_desc": "Average Disk response time - System", "kpi_group": "OS", "parent": true, "unit": "MS", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_resp_time", "kpi_desc": "Disk Response time", "kpi_group": "OS", "parent": false, "unit": "MS", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serialnr", "kpi_desc": "Serial No. - Single Disk", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_transfer", "kpi_desc": "Disk Transfer speed", "kpi_group": "OS", "parent": false, "unit": "KB/S", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_util", "kpi_desc": "Disk Utilization", "kpi_group": "OS", "parent": false, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_wait_time", "kpi_desc": "Average disk wait - System", "kpi_group": "OS", "parent": true, "unit": "MS", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "free_mem_perc", "kpi_desc": "Free Physical memory percentage", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_collissions", "kpi_desc": "LAN Collissions - System Level", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_collissions", "kpi_desc": "LAN Collissions - Node Level", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_errors", "kpi_desc": "LAN Errors Inbound - Node Level", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_lanname", "kpi_desc": "LAN Name - Node Level", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_errors", "kpi_desc": "LAN Errors Outbound - Node Level", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_packets", "kpi_desc": "LAN Packets Out - Node Level", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_serialnr", "kpi_desc": "LAN Serial No. - Node Level", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_page_in", "kpi_desc": "Pages IN in kB/s", "kpi_group": "OS", "parent": true, "unit": "KB/S", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_cpu_time_avg", "kpi_desc": "Top 40 Processes - CPU Time Average", "kpi_group": "OS", "parent": true, "unit": "S", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_command", "kpi_desc": "Top 40 CPU Processes - Process Name", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_time", "kpi_desc": "Top 40 CPU Processes - CPU Time", "kpi_group": "OS", "parent": false, "unit": "S", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_res_size", "kpi_desc": "Top 40 CPU Processes - Memory Utilization", "kpi_group": "OS", "parent": false, "unit": "KB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_idle", "kpi_desc": "Single CPU Idle Time", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_user", "kpi_desc": "Single CPU used by User", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_name", "kpi_desc": "Name of the Disk", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_queue_len", "kpi_desc": "Disk Avg. Queue length", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serialnr", "kpi_desc": "Serial No. - Single Disk", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_lanname", "kpi_desc": "LAN Name - Node Level", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_errors", "kpi_desc": "LAN Errors Outbound - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_packets", "kpi_desc": "LAN Packets Out - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_command", "kpi_desc": "Top 40 CPU Processes - Process Name", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_util", "kpi_desc": "Top 40 CPU Processes - CPU Utilization %", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priority", "kpi_desc": "Top 40 CPU Processes - Priority", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priv_pages", "kpi_desc": "Top 40 CPU Processes - Process Pages", "kpi_group": "OS", "parent": "", "unit": "KB", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_serialnr", "kpi_desc": "Top 40 CPU Processes - Serial No.", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_username", "kpi_desc": "Top 40 CPU Processes - Username", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_serial", "kpi_desc": "Single CPU Serial No.", "kpi_group": "OS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_user", "kpi_desc": "Single CPU used by User", "kpi_group": "OS", "parent": "", "unit": "PERC", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_name", "kpi_desc": "Name of the Disk", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_ops_sec", "kpi_desc": "Disk Operations per second", "kpi_group": "OS", "parent": "", "unit": "/S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_resp_time", "kpi_desc": "Disk Response time", "kpi_group": "OS", "parent": "", "unit": "MS", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serialnr", "kpi_desc": "Serial No. - Single Disk", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_errors", "kpi_desc": "LAN Errors Outbound - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_out_packets", "kpi_desc": "LAN Packets Out - Node Level", "kpi_group": "OS", "parent": "", "unit": "COUNT", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_command", "kpi_desc": "Top 40 CPU Processes - Process Name", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_time", "kpi_desc": "Top 40 CPU Processes - CPU Time", "kpi_group": "OS", "parent": "", "unit": "S", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priority", "kpi_desc": "Top 40 CPU Processes - Priority", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priv_pages", "kpi_desc": "Top 40 CPU Processes - Process Pages", "kpi_group": "OS", "parent": "", "unit": "KB", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_username", "kpi_desc": "Top 40 CPU Processes - Username", "kpi_group": "OS", "parent": "", "unit": "N/A", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_idle", "kpi_desc": "CPU True Idle", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_load5_avg", "kpi_desc": "CPU Load - 5 Mins Average", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_sys", "kpi_desc": "CPU - System Utilization", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": true, "filter": false, "g2y": 80, "y2r": 90, "direction": "GT", "criticality": ""}, {"kpi_name": "cpu_s_idle", "kpi_desc": "Single CPU Idle Time", "kpi_group": "OS", "parent": false, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_serial", "kpi_desc": "Single CPU Serial No.", "kpi_group": "OS", "parent": false, "unit": "ID", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "cpu_s_user", "kpi_desc": "Single CPU used by User", "kpi_group": "OS", "parent": false, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_ops_sec", "kpi_desc": "Average Disk operations per Second - System", "kpi_group": "OS", "parent": true, "unit": "/S", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_queue_len", "kpi_desc": "Average Disk Queue Length - System", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_serv_time", "kpi_desc": "Average Disk Service Time - System", "kpi_group": "OS", "parent": true, "unit": "MS", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_name", "kpi_desc": "Name of the Disk", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_ops_sec", "kpi_desc": "Disk Operations per second", "kpi_group": "OS", "parent": false, "unit": "/S", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_queue_len", "kpi_desc": "Disk Avg. Queue length", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_serv_time", "kpi_desc": "Disk Avg. Service time", "kpi_group": "OS", "parent": false, "unit": "MS", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_s_wait_time", "kpi_desc": "Disk Avg. wait time - Single Disk", "kpi_group": "OS", "parent": false, "unit": "MS", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_transfer", "kpi_desc": "Average Disk Transfer Speed - System", "kpi_group": "OS", "parent": true, "unit": "KB/S", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "disk_util", "kpi_desc": "Average Disk utilization - System", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_in_errors", "kpi_desc": "LAN Errors Inbound - System Level", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_in_packets", "kpi_desc": "LAN Packets In - System Level", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_out_errors", "kpi_desc": "LAN Errors outbound - System Level", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_out_packets", "kpi_desc": "LAN Packets Out - System Level", "kpi_group": "OS", "parent": true, "unit": "COUNT", "drilldown": true, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "lan_s_in_packets", "kpi_desc": "LAN Packets In - Node Level", "kpi_group": "OS", "parent": false, "unit": "COUNT", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_free", "kpi_desc": "Free Physical memory", "kpi_group": "OS", "parent": true, "unit": "KB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_phys", "kpi_desc": "Total Physical memory", "kpi_group": "OS", "parent": true, "unit": "MB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_page_out", "kpi_desc": "Pages OUT in kB/s", "kpi_group": "OS", "parent": true, "unit": "KB/S", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_swap_conf", "kpi_desc": "Configured Swap memory size", "kpi_group": "OS", "parent": true, "unit": "MB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_swap_free_perc", "kpi_desc": "Free Swap Memory percentage", "kpi_group": "OS", "parent": true, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_swap_max", "kpi_desc": "Maximum Swap size", "kpi_group": "OS", "parent": true, "unit": "MB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "mem_swap_size", "kpi_desc": "Actual Swap size", "kpi_group": "OS", "parent": true, "unit": "MB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_mem_util_avg", "kpi_desc": "Top 40 Processes - Memory Util. Average", "kpi_group": "OS", "parent": true, "unit": "KB", "drilldown": false, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_cpu_util", "kpi_desc": "Top 40 CPU Processes - CPU Utilization %", "kpi_group": "OS", "parent": false, "unit": "PERC", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priority", "kpi_desc": "Top 40 CPU Processes - Priority", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_priv_pages", "kpi_desc": "Top 40 CPU Processes - Process Pages", "kpi_group": "OS", "parent": false, "unit": "KB", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_proc_id", "kpi_desc": "Top 40 CPU Processes - Process ID", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_serialnr", "kpi_desc": "Top 40 CPU Processes - Serial No.", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "top_s_username", "kpi_desc": "Top 40 CPU Processes - Username", "kpi_group": "OS", "parent": false, "unit": "N/A", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}]