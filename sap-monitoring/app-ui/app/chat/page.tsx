"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { useChat } from "@ai-sdk/react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import {
  Send,
  Paperclip,
  FileText,
  Smile,
  Mic,
  X,
  Reply,
  MoreHorizontal,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

// Message type definition
type MessageType = {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
  attachments?: Attachment[]
  replyTo?: string
  isNew?: boolean
}

type Attachment = {
  id: string
  type: "image" | "pdf"
  name: string
  url: string
  size?: string
}

export default function ChatInterface() {
  const { messages: aiMessages, input, handleInputChange, handleSubmit: handleAiSubmit } = useChat()

  const [messages, setMessages] = useState<MessageType[]>([
    {
      id: "welcome",
      content: "Hello! I'm your AI assistant. How can I help you today?",
      role: "assistant",
      timestamp: new Date(),
      isNew: false,
    },
  ])
  const [attachments, setAttachments] = useState<Attachment[]>([])
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // Convert AI SDK messages to our enhanced message format
  useEffect(() => {
    const enhancedMessages = aiMessages.map((msg) => ({
      id: msg.id,
      content: msg.content,
      role: msg.role as "user" | "assistant",
      timestamp: new Date(),
      attachments: [],
    }))

    if (enhancedMessages.length > messages.length) {
      setMessages(enhancedMessages)
    }
  }, [aiMessages])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleFileClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files) return

    const newAttachments: Attachment[] = []

    Array.from(files).forEach((file) => {
      const fileType = file.type.startsWith("image/") ? "image" : "pdf"
      if (fileType === "image" || file.type === "application/pdf") {
        const fileUrl = URL.createObjectURL(file)
        newAttachments.push({
          id: Math.random().toString(36).substring(2, 9),
          type: fileType,
          name: file.name,
          url: fileUrl,
          size: formatFileSize(file.size),
        })
      }
    })

    setAttachments([...attachments, ...newAttachments])

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B"
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB"
    else return (bytes / 1048576).toFixed(1) + " MB"
  }

  const removeAttachment = (id: string) => {
    setAttachments(attachments.filter((attachment) => attachment.id !== id))
  }

  const cancelReply = () => {
    setReplyingTo(null)
  }

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()

    if (!inputValue.trim() && attachments.length === 0) return

    const newMessage: MessageType = {
      id: Date.now().toString(),
      content: inputValue,
      role: "user",
      timestamp: new Date(),
      attachments: attachments.length > 0 ? [...attachments] : undefined,
      replyTo: replyingTo || undefined,
      isNew: true,
    }

    setMessages([...messages, newMessage])
    setInputValue("")
    setAttachments([])
    setReplyingTo(null)

    // Simulate AI typing
    setIsTyping(true)

    // Simulate AI response after a short delay
    setTimeout(() => {
      setIsTyping(false)
      const aiResponse: MessageType = {
        id: (Date.now() + 1).toString(),
        content:
          "Thanks for your message! This is a simulated response from the AI assistant. In a real implementation, this would be generated by the AI model.",
        role: "assistant",
        timestamp: new Date(),
        isNew: true,
      }
      setMessages((prev) => [...prev, aiResponse])
    }, 2000)
  }

  const handleReply = (messageId: string) => {
    setReplyingTo(messageId)
  }

  const getReplyingToMessage = () => {
    if (!replyingTo) return null
    return messages.find((msg) => msg.id === replyingTo)
  }

  const replyingToMessage = getReplyingToMessage()

  return (
    <div className="flex h-screen bg-gradient-to-br from-background via-background/98 to-background/95 overflow-hidden">
      <main className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-8 py-6 relative h-full">
          {/* Header */}
          <div className="flex flex-col mb-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center border border-border">
                    <Sparkles className="h-5 w-5 text-primary" />
                  </div>
                  <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></span>
                </div>
                <div>
                  <h1 className="font-semibold text-xl text-foreground">AI Assistant</h1>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <span className="inline-block w-1.5 h-1.5 rounded-full bg-green-500 mr-1.5"></span>
                    Online
                  </div>
                </div>
              </div>
              <div>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Chat Area */}
          <div 
            className="flex-1 overflow-hidden pb-32 custom-scrollbar"
            style={{ height: "calc(100vh - 180px)" }}
          >
            <div
              ref={chatContainerRef}
              className="h-full overflow-y-auto pr-4 custom-scrollbar"
            >
              <div className="space-y-6">
                <AnimatePresence mode="popLayout" initial={false}>
                  {messages.map((message, index) => (
                    <motion.div
                      key={message.id}
                      initial={message.isNew ? { opacity: 0, y: 20 } : false}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-2"
                    >
                      {message.replyTo && (
                        <div
                          className={`${
                            message.role === "user" ? "ml-12 mr-4" : "ml-4 mr-12"
                          } p-2 rounded-lg bg-muted/50 text-sm max-w-[80%] border border-border/40`}
                        >
                          <p className="font-medium text-xs text-muted-foreground">
                            Replying to {message.replyTo === "user" ? "yourself" : "AI Assistant"}
                          </p>
                          <p className="truncate text-foreground/80">{message.replyTo}</p>
                        </div>
                      )}

                      <div className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                        <div
                          className={`flex ${
                            message.role === "user" ? "flex-row-reverse" : "flex-row"
                          } items-end gap-2 max-w-[85%]`}
                        >
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              message.role === "user"
                                ? "bg-primary text-primary-foreground"
                                : "bg-secondary text-secondary-foreground"
                            }`}
                          >
                            {message.role === "user" ? "U" : "AI"}
                          </div>

                          <div className="space-y-1 max-w-full">
                            <div
                              className={cn(
                                "p-3 rounded-2xl",
                                message.role === "user" 
                                  ? "bg-primary text-primary-foreground" 
                                  : "bg-card border border-border shadow-sm"
                              )}
                            >
                              <p className="whitespace-pre-wrap break-words">{message.content}</p>

                              {message.attachments && message.attachments.length > 0 && (
                                <div className="mt-3 grid grid-cols-2 gap-2">
                                  {message.attachments.map((attachment) => (
                                    <div key={attachment.id} className="relative">
                                      {attachment.type === "image" ? (
                                        <div className="relative aspect-square rounded-lg overflow-hidden border border-border bg-muted/20">
                                          <img
                                            src={attachment.url || "/placeholder.svg"}
                                            alt={attachment.name}
                                            className="object-cover w-full h-full"
                                          />
                                        </div>
                                      ) : (
                                        <div className="flex items-center p-2 rounded-lg bg-muted/20 border border-border">
                                          <FileText className="h-5 w-5 mr-2 text-muted-foreground" />
                                          <div className="overflow-hidden">
                                            <p className="text-sm font-medium truncate">{attachment.name}</p>
                                            <p className="text-xs text-muted-foreground">{attachment.size}</p>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>

                            <div
                              className={`flex text-xs text-muted-foreground ${
                                message.role === "user" ? "justify-end" : "justify-start"
                              }`}
                            >
                              <span>
                                {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {message.role === "assistant" && (
                        <div className="flex items-center gap-1 ml-10 mt-1">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-7 w-7" 
                            onClick={() => handleReply(message.id)}
                          >
                            <Reply className="h-3.5 w-3.5" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <ThumbsUp className="h-3.5 w-3.5" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <ThumbsDown className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </AnimatePresence>

                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-end gap-2"
                  >
                    <div className="w-8 h-8 rounded-full flex items-center justify-center bg-secondary text-secondary-foreground">
                      AI
                    </div>
                    <div className="p-4 rounded-2xl bg-card border border-border shadow-sm">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: "0ms" }} />
                        <div className="w-2 h-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: "150ms" }} />
                        <div className="w-2 h-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: "300ms" }} />
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>
          </div>

          {/* Input Area - Fixed at bottom */}
          <div className="absolute bottom-0 left-0 right-0 bg-background/80 backdrop-blur-md border-t border-border p-4">
            <div className="container mx-auto px-4">
              {replyingToMessage && (
                <AnimatePresence>
                  <motion.div
                    key="reply-container"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mb-2 p-2 rounded-lg bg-muted/50 flex justify-between items-start border border-border/40"
                  >
                    <div className="flex-1">
                      <p className="text-xs font-medium text-muted-foreground">
                        Replying to {replyingToMessage.role === "user" ? "yourself" : "AI Assistant"}
                      </p>
                      <p className="text-sm truncate text-foreground/80">{replyingToMessage.content}</p>
                    </div>
                    <Button variant="ghost" size="icon" className="h-7 w-7" onClick={cancelReply}>
                      <X className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </AnimatePresence>
              )}

              {attachments.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-3 flex flex-wrap gap-2"
                >
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="relative group">
                      {attachment.type === "image" ? (
                        <div className="relative h-16 w-16 rounded-lg overflow-hidden border border-border group-hover:border-border-border/80 transition-colors">
                          <img
                            src={attachment.url || "/placeholder.svg"}
                            alt={attachment.name}
                            className="object-cover w-full h-full"
                          />
                          <Button
                            variant="ghost" 
                            size="icon" 
                            className="absolute top-0 right-0 h-6 w-6 bg-background/40 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => removeAttachment(attachment.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center p-2 rounded-lg bg-muted/30 border border-border group-hover:border-border/80 transition-colors pr-8">
                          <FileText className="h-5 w-5 mr-2 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium truncate max-w-[100px]">{attachment.name}</p>
                            <p className="text-xs text-muted-foreground">{attachment.size}</p>
                          </div>
                          <Button
                            variant="ghost" 
                            size="icon" 
                            className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => removeAttachment(attachment.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </motion.div>
              )}

              <form onSubmit={handleSendMessage} className="relative">
                <Card className="relative flex items-end rounded-md bg-card border border-border/50 focus-within:border-ring overflow-hidden p-0">
                  <Textarea
                    placeholder="Type your message..."
                    className="flex-1 h-full min-h-[56px] resize-none py-3 px-4 border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    rows={1}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage(e)
                      }
                    }}
                  />
                  <div className="absolute bottom-1 right-1 flex items-center gap-1 mr-1">
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      className="hidden"
                      accept="image/*,application/pdf"
                      multiple
                    />
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8"
                      onClick={handleFileClick}
                    >
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                    <Button 
                      type="submit" 
                      size="icon"
                      className="h-8 w-8 rounded-md"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              </form>

              <div className="mt-2 text-xs text-center text-muted-foreground">
                AI responses are simulated. Press Enter to send, Shift+Enter for a new line.
              </div>
            </div>
          </div>

          {/* Floating action button */}
          <div className="absolute bottom-24 right-8">
            <Button size="icon" className="h-10 w-10 rounded-full shadow-lg">
              <Mic className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
