import { ThemeProvider as NextThemeProvider } from "@/components/theme-provider";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ThemeToggleProvider } from "@/contexts/ThemeToggleContext";
import { FontProvider } from "@/contexts/FontContext";
import "./globals.css";
import { Sidebar, SidebarProvider } from "@/components/sidebar";
import { Toaster } from "sonner";
import { cn } from "@/lib/utils";
import { fontSans } from "@/lib/fonts";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          fontSans.variable
        )}
      >
        <NextThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ThemeProvider>
            <ThemeToggleProvider>
              <FontProvider>
                <SidebarProvider>
                  <div className="bg-background min-h-screen">
                    <div className="flex relative">
                      <Sidebar />
                      <div className="flex flex-col w-full min-h-screen transition-all duration-300" id="main-content-wrapper">
                        {children}
                      </div>
                    </div>
                  </div>
                </SidebarProvider>
              </FontProvider>
            </ThemeToggleProvider>
          </ThemeProvider>
        </NextThemeProvider>
        <Toaster richColors position="top-right" />
      </body>
    </html>
  );
}
