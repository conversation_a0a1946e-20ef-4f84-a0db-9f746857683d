{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node src/index.ts"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.4.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "zod": "^3.24.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.9", "prisma": "^6.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}