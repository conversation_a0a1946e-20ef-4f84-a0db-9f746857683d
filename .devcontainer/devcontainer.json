// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/javascript-node
{
  "name": "FastLane",
  // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
  "build": {
    "dockerfile": "Dockerfile"
  },
  // "image": "mcr.microsoft.com/devcontainers/javascript-node:1-22-bookworm",

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [4004],

  // Use 'postCreateCommand' to run commands after the container is created.
  // "postCreateCommand": "yarn install",

  // Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": [
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        "christian-kohler.npm-intellisense",
        "christian-kohler.path-intellisense",
        // "streetsidesoftware.code-spell-checker",
        "formulahendry.auto-rename-tag",
        "ms-vscode.js-debug",
        "eamodio.gitlens"
      ],
      "settings": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": true
        },
        "javascript.updateImportsOnFileMove.enabled": "always",
        "editor.bracketPairColorization.enabled": true,
        "editor.guides.bracketPairs": true,
        "files.trimTrailingWhitespace": true,
        "terminal.integrated.defaultProfile.linux": "bash"
      }
    }
  }

  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "root"
}
