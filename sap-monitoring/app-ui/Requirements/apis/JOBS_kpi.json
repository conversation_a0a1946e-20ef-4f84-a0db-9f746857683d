[{"kpi_name": "job_s_name", "kpi_desc": "Job Name", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_count", "kpi_desc": "Job Counter", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strtdt", "kpi_desc": "Job Scheduled Start Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchdt", "kpi_desc": "Job Last changed Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_date", "kpi_desc": "Job Release Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strttm", "kpi_desc": "Job Actual Start Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_endtm", "kpi_desc": "Job Actual End Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_status", "kpi_desc": "Job Status", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_server", "kpi_desc": "Job execution Server", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_name", "kpi_desc": "Job Name", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_count", "kpi_desc": "Job Counter", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strtdt", "kpi_desc": "Job Scheduled Start Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strttm", "kpi_desc": "Job Scheduled Start Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchdt", "kpi_desc": "Job Last changed Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_date", "kpi_desc": "Job Release Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_time", "kpi_desc": "Job Release Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strttm", "kpi_desc": "Job Actual Start Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_enddt", "kpi_desc": "Job Actual End Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_endtm", "kpi_desc": "Job Actual End Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_status", "kpi_desc": "Job Status", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_delayed", "kpi_desc": "Number of  Background Jobs delayed", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_cancelled", "kpi_desc": "Number of canvelled Background Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_completed", "kpi_desc": "Number of completed Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_scheduled", "kpi_desc": "Number of Scheduled Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_name", "kpi_desc": "Job Name", "kpi_group": "JOBS", "parent": false, "unit": "ID", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strttm", "kpi_desc": "Job Scheduled Start Time", "kpi_group": "JOBS", "parent": false, "unit": "Time", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchdt", "kpi_desc": "Job Last changed Date", "kpi_group": "JOBS", "parent": false, "unit": "Date", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchtm", "kpi_desc": "Job Last changed Time", "kpi_group": "JOBS", "parent": false, "unit": "Time", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_date", "kpi_desc": "Job Release Date", "kpi_group": "JOBS", "parent": false, "unit": "Date", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_time", "kpi_desc": "Job Release Time", "kpi_group": "JOBS", "parent": false, "unit": "Time", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strtdt", "kpi_desc": "Job Actual Start Date", "kpi_group": "JOBS", "parent": false, "unit": "Date", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strttm", "kpi_desc": "Job Actual Start Time", "kpi_group": "JOBS", "parent": false, "unit": "Time", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_enddt", "kpi_desc": "Job Actual End Date", "kpi_group": "JOBS", "parent": false, "unit": "Date", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strttm", "kpi_desc": "Job Scheduled Start Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchtm", "kpi_desc": "Job Last changed Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_rel_time", "kpi_desc": "Job Release Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strtdt", "kpi_desc": "Job Actual Start Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_enddt", "kpi_desc": "Job Actual End Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_lastchtm", "kpi_desc": "Job Last changed Time", "kpi_group": "JOBS", "parent": "", "unit": "Time", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_strtdt", "kpi_desc": "Job Actual Start Date", "kpi_group": "JOBS", "parent": "", "unit": "Date", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_server", "kpi_desc": "Job execution Server", "kpi_group": "JOBS", "parent": "", "unit": "ID", "drilldown": "", "filter": "", "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_max_delay", "kpi_desc": "Maximum delay to start a Job", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_delayed_time", "kpi_desc": "Total delay for the run period ", "kpi_group": "JOBS", "parent": true, "unit": "S", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_active", "kpi_desc": "Number of Active Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_total", "kpi_desc": "Number of Total Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_released", "kpi_desc": "Number of Released Jobs", "kpi_group": "JOBS", "parent": true, "unit": "Count", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_max_runtime", "kpi_desc": "Maximum Runtime taken by a Job", "kpi_group": "JOBS", "parent": true, "unit": "S", "drilldown": true, "filter": true, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_count", "kpi_desc": "Job Counter", "kpi_group": "JOBS", "parent": false, "unit": "ID", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_schd_strtdt", "kpi_desc": "Job Scheduled Start Date", "kpi_group": "JOBS", "parent": false, "unit": "Date", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_act_endtm", "kpi_desc": "Job Actual End Time", "kpi_group": "JOBS", "parent": false, "unit": "Time", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_status", "kpi_desc": "Job Status", "kpi_group": "JOBS", "parent": false, "unit": "ID", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}, {"kpi_name": "job_s_server", "kpi_desc": "Job execution Server", "kpi_group": "JOBS", "parent": false, "unit": "ID", "drilldown": false, "filter": false, "g2y": null, "y2r": null, "direction": "", "criticality": ""}]