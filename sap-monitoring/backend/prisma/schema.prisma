datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Systems {
  id               Int              @id @default(autoincrement())
  systemId         String           @unique
  client           String
  systemName       String
  systemUrl        String
  systemType       String
  pollingStatus    String
  connectionStatus String
  description      String?
  createdAt        DateTime         @default(now())
  createdBy        String?
  updatedAt        DateTime?
  updatedBy        String?
  MonitoringAreas  MonitoringArea[]
  KpiGroups        KpiGroup[]
  Kpis             Kpi[]
}

model SystemsDummy {
  id               Int       @id @default(autoincrement())
  systemId         String    @unique
  client           String
  systemName       String
  systemUrl        String
  systemType       String
  pollingStatus    String
  connectionStatus String
  description      String?
  userID           String
  password         String
  createdAt        DateTime  @default(now())
  createdBy        String?
  updatedAt        DateTime?
  updatedBy        String?
}

model MonitoringArea {
  areaId      Int        @id @default(autoincrement())
  id          String     @unique
  areaName    String
  description String?
  createdAt   DateTime   @default(now())
  createdBy   String?
  updatedAt   DateTime?
  updatedBy   String?
  systemId    String
  system      Systems    @relation(fields: [systemId], references: [systemId], onDelete: Cascade)
  KpiGroups   KpiGroup[]
  Kpis        Kpi[]
}

model MonitoringKpi {
  id                  Int      @id @default(autoincrement())
  systemId            String?  @db.VarChar(50)
  client              Int?
  monitoringArea      String?  @db.VarChar(100)
  kpiGroup            String?  @db.VarChar(100)
  kpiName             String?  @db.VarChar(100)
  parentKpi           String?  @db.VarChar(100)
  kpiDescription      String?  @db.Text
  dataType            String?  @db.VarChar(50)
  unit                String?  @db.VarChar(50)
  aggregation         String?  @db.VarChar(50)
  drilldown           Boolean?
  filter              Boolean?
  secondLevelDetails  Json?
  filterValues        Json?
  drilldownCondition  Json?

  @@map("monitoring_kpi")
}


model KpiGroup {
  groupId          Int            @id @default(autoincrement())
  areaId           Int
  id               String         @unique
  groupName        String
  description      String?
  createdAt        DateTime       @default(now())
  createdBy        String?
  updatedAt        DateTime?
  updatedBy        String?
  monitoringAreaId String
  monitoringArea   MonitoringArea @relation(fields: [monitoringAreaId], references: [id], onDelete: Cascade)
  systemId         String
  system           Systems        @relation(fields: [systemId], references: [systemId], onDelete: Cascade)
  Kpis             Kpi[]
}

model Kpi {
  kpiId            Int            @id @default(autoincrement())
  groupId          Int
  areaId           Int
  id               String         @unique
  kpiName          String
  unit             String?
  description      String?
  createdAt        DateTime       @default(now())
  createdBy        String?
  updatedAt        DateTime?
  updatedBy        String?
  kpiGroupId       Int
  kpiGroup         KpiGroup       @relation(fields: [kpiGroupId], references: [groupId], onDelete: Cascade)
  monitoringAreaId String
  monitoringArea   MonitoringArea @relation(fields: [monitoringAreaId], references: [id], onDelete: Cascade)
  systemId         String
  system           Systems        @relation(fields: [systemId], references: [systemId], onDelete: Cascade)
}

model KpiHierarchy {
  id               Int      @id @default(autoincrement())
  sid              String
  monitoringArea   String
  kpiGroup         String
  kpiName          String
  kpiDescription   String?
  createdAt        DateTime? @default(now())
  updatedAt        DateTime? @updatedAt

  @@unique([sid, monitoringArea, kpiGroup, kpiName])
  @@map("kpi_hierarchy")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  system      String
  timeRange   String
  resolution  String
  isDefault   Boolean  @default(false)
  isFavorite  Boolean  @default(false)
  graphs      Json     // JSONB field for storing graphs
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?
  updatedBy   String?

  @@index([graphs], type: Gin) // Add GIN index for JSONB querying
  @@map("templates")
}


